{"Root": {"desc": "A component at the root of any logged-in pages. Can be used for creating modals and pop-ups that display over the whole app. Does not override any existing component.", "props": {}}, "PostTypePlugin": {"desc": "The component displaying the message body of posts. For adding new components based on post type. Use in your `postTypeComponents` argument to `registerComponents` in your plugin's intialization function.", "props": {"post": {"type": {"name": "object"}, "required": true, "desc": "Post to be displayed"}, "compactDisplay": {"type": {"name": "bool"}, "required": false, "defaultValue": false, "desc": "Set to true if UI is in compact display mode"}, "isRHS": {"type": {"name": "bool"}, "required": true, "defaultValue": false, "desc": "Set to true if the post is in the right-hand sidebar (thread, search results, etc.)"}}}, "ChannelHeaderButton": {"desc": "A component on the right side of the channel header, beside buttons such as the pinned posts button.", "props": {}}, "MobileChannelHeaderButton": {"desc": "Same as ChannelHeaderButton, except shown in mobile view when the screen is less than 768 pixels wide.", "props": {}}}