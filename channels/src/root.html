<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">

    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <meta name='robots' content='noindex, nofollow'>
    <meta name='referrer' content='no-referrer'>

    <title>مساحة عمل سوفا</title>

    <meta name='mobile-web-app-capable' content='yes'>
    <meta name='application-name' content='Sofa Workspace'>
    <meta name='format-detection' content='telephone=no'>

    <link rel="icon" type="image/png" href="/static/images/favicon/favicon-default-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="/static/images/favicon/favicon-default-24x24.png" sizes="24x24">
    <link rel="icon" type="image/png" href="/static/images/favicon/favicon-default-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="/static/images/favicon/favicon-default-64x64.png" sizes="64x64">
    <link rel="icon" type="image/png" href="/static/images/favicon/favicon-default-96x96.png" sizes="96x96">

    <!-- Initialize subpath empty script for subpath support which will be replaced by server/channels/utils/subpath.go -->
    <script id="publicPathInWindowScript"></script>

    <!--
        This "empty" link element is used as a dynamic inject placeholder for the syntax highlighting css styles:
        1. The highlight.js lib finds this element by its 'code_theme' class.
        2. The lib dynamically defines the 'href' attribute and its value to load the required CSS for the detected language by its detection logic.
        3. This allows it to do at runtime theme switching without having to modify the DOM structure.
        4. The 'href' attribute is empty to prevent unnecessary HTTP requests.
    -->
    <link rel='stylesheet' class='code_theme'>

    <!-- We need this CSS first to show the initial loading screen -->
    <link id="initialLoadingScreenCSS" rel='stylesheet' href='/static/css/initial_loading_screen.css'>
    <meta name="theme-color" content="#3db887">
    <meta name="apple-mobile-web-app-status-bar-style" content="#3db887">
</head>
<body class='font--open_sans enable-animations'>
    <%= require('html-loader!./components/initial_loading_screen/initial_loading_screen_template.html').default %>
    <div id='root'></div>
    <div id='root-portal'></div>
     <%= require('html-loader!./components/no_script/no_script_template.html').default %>
</body>
</html>
