{"name": "mattermost-webapp", "browser": {"./client/web_client.jsx": "./client/browser_web_client.jsx"}, "version": "9.3.0", "private": true, "dependencies": {"@floating-ui/react": "0.26.28", "@giphy/js-fetch-api": "5.1.0", "@giphy/react-components": "8.1.0", "@guyplusplus/turndown-plugin-gfm": "1.0.7", "@mattermost/client": "*", "@mattermost/compass-components": "^0.2.12", "@mattermost/compass-icons": "0.1.39", "@mattermost/desktop-api": "5.10.0-2", "@mattermost/types": "*", "@mui/base": "5.0.0-alpha.127", "@mui/material": "5.11.16", "@mui/styled-engine-sc": "5.11.11", "@tanstack/react-table": "8.10.7", "@tippyjs/react": "4.2.6", "@types/color-hash": "1.0.2", "@types/turndown": "5.0.5", "bootstrap": "3.4.1", "buffer": "6.0.3", "chart.js": "3.8.2", "classnames": "2.3.2", "color-contrast-checker": "2.1.0", "color-hash": "2.0.1", "core-js": "3.26.0", "country-list": "2.2.0", "crypto-browserify": "3.12.0", "css-vars-ponyfill": "2.4.8", "date-fns": "2.29.3", "dynamic-virtualized-list": "github:mattermost/dynamic-virtualized-list#3fe918b41de4cb08dbf43f1207bb58827b38e833", "emoji-regex": "10.2.1", "exif2css": "1.3.0", "fast-deep-equal": "3.1.3", "flexsearch": "0.6.32", "font-awesome": "4.7.0", "highlight.js": "11.6.0", "history": "4.10.1", "hoist-non-react-statics": "3.3.2", "html-to-react": "1.6.0", "inobounce": "0.2.1", "ipaddr.js": "2.1.0", "katex": "0.16.10", "key-mirror": "1.0.1", "localforage": "1.10.0", "localforage-observable": "2.1.1", "lodash": "4.17.21", "luxon": "3.3.0", "mark.js": "8.11.1", "marked": "github:mattermost/marked#2ef7f28cc7718e3f551c4ce9ea75fdd7580c2008", "memoize-one": "6.0.0", "moment-timezone": "0.5.38", "p-queue": "7.3.0", "pdfjs-dist": "4.4.168", "popper.js": "1.16.1", "process": "0.11.10", "prop-types": "15.8.1", "react": "17.0.2", "react-beautiful-dnd": "13.1.1", "react-bootstrap": "github:mattermost/react-bootstrap#d821e2b1db1059bd36112d7587fd1b0912b27626", "react-color": "2.19.3", "react-contextmenu": "2.14.0", "react-custom-scrollbars": "4.2.1", "react-day-picker": "8.3.6", "react-dom": "17.0.2", "react-hot-loader": "4.13.0", "react-intl": "*", "react-is": "17.0.2", "react-overlays": "0.9.3", "react-popper": "2.3.0", "react-redux": "7.2.4", "react-router-dom": "5.3.4", "react-select": "5.10.2", "react-transition-group": "4.4.5", "react-virtualized-auto-sizer": "1.0.7", "react-window": "1.8.8", "react-window-infinite-loader": "1.0.8", "rebound": "0.1.0", "redux": "4.2.0", "redux-batched-actions": "0.5.0", "redux-persist": "6.0.0", "redux-thunk": "2.4.2", "regenerator-runtime": "0.13.10", "rudder-sdk-js": "1.0.16", "semver": "7.6.3", "serialize-error": "11.0.3", "shallow-equals": "1.0.0", "smooth-scroll-into-view-if-needed": "2.0.2", "stream-browserify": "3.0.0", "styled-components": "5.3.6", "timezones.json": "1.7.1", "tinycolor2": "1.6.0", "turndown": "7.2.0", "web-vitals": "4.2.4", "zen-observable": "0.10.0"}, "devDependencies": {"@deanwhillier/jest-matchmedia-mock": "1.2.0", "@hot-loader/react-dom": "17.0.2", "@mattermost/calls-common": "0.27.0", "@mattermost/eslint-plugin": "*", "@mattermost/mmjstool": "github:mattermost/mattermost-utilities#7b63833d208d482ba4a1c12230bb3e68dd9c5e5e", "@redux-devtools/extension": "3.2.3", "@stylistic/stylelint-plugin": "2.1.0", "@testing-library/jest-dom": "5.16.4", "@testing-library/react": "12.1.4", "@testing-library/react-hooks": "8.0.1", "@testing-library/user-event": "13.5.0", "@types/bootstrap": "4.5.0", "@types/country-list": "2.1.0", "@types/enzyme": "3.10.11", "@types/jest": "28.1.8", "@types/katex": "0.11.0", "@types/lodash": "4.14.202", "@types/luxon": "3.0.2", "@types/mark.js": "8.11.6", "@types/marked": "0.7.4", "@types/react": "17.0.83", "@types/react-beautiful-dnd": "13.1.2", "@types/react-bootstrap": "0.32.35", "@types/react-color": "3.0.6", "@types/react-custom-scrollbars": "4.0.10", "@types/react-dom": "17.0.25", "@types/react-is": "17.0.2", "@types/react-overlays": "1.1.3", "@types/react-redux": "7.1.31", "@types/react-router-dom": "5.3.3", "@types/react-transition-group": "4.4.5", "@types/react-virtualized-auto-sizer": "1.0.1", "@types/react-window": "1.8.5", "@types/react-window-infinite-loader": "1.0.6", "@types/redux-mock-store": "1.0.3", "@types/regenerator-runtime": "0.13.8", "@types/semver": "7.5.8", "@types/shallow-equals": "1.0.3", "@types/styled-components": "5.1.32", "@types/tinycolor2": "1.4.6", "copy-webpack-plugin": "11.0.0", "emoji-datasource": "6.1.1", "emoji-datasource-apple": "6.1.1", "emoji-datasource-google": "6.1.1", "enzyme": "3.11.0", "enzyme-adapter-react-17-updated": "1.0.2", "enzyme-to-json": "3.6.2", "eslint-plugin-no-only-tests": "3.1.0", "external-remotes-plugin": "1.0.0", "html-loader": "5.1.0", "html-webpack-plugin": "5.5.0", "identity-obj-proxy": "3.0.0", "image-webpack-loader": "8.1.0", "imagemin-gifsicle": "7.0.0", "imagemin-mozjpeg": "9.0.0", "isomorphic-fetch": "3.0.0", "jest": "29.7.0", "jest-canvas-mock": "2.5.0", "jest-cli": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-junit": "16.0.0", "jest-watch-typeahead": "2.2.2", "nock": "13.2.8", "prettier": "2.3.2", "react-router-enzyme-context": "1.2.0", "redux-mock-store": "1.5.4", "redux-persist-node-storage": "2.0.0", "stylelint": "16.10.0", "stylelint-config-idiomatic-order": "10.0.0", "stylelint-config-recommended-scss": "14.1.0", "stylelint-order": "6.0.4", "stylelint-scss": "6.8.1", "webpack-pwa-manifest": "4.3.0", "yargs": "17.7.2"}, "overrides": {"@mattermost/desktop-api": {"typescript": "$typescript"}}, "scripts": {"check": "npm run check:eslint && npm run check:stylelint", "check:eslint": "eslint --ext .js,.jsx,.tsx,.ts ./src --quiet --cache", "check:stylelint": "stylelint \"**/*.{css,scss}\" --cache", "fix": "eslint --ext .js,.jsx,.tsx,.ts ./src --quiet --fix --cache && stylelint \"**/*.{css,scss}\" --fix --cache", "build": "cross-env NODE_ENV=production webpack", "run": "webpack --progress --watch", "dev-server": "webpack serve --mode development", "test": "cross-env TZ=Etc/UTC jest", "test:watch": "cross-env TZ=Etc/UTC jest --watch", "test:updatesnapshot": "cross-env TZ=Etc/UTC jest --updateSnapshot", "test:debug": "cross-env TZ=Etc/UTC jest --forceExit --detectOpenHandles --verbose", "test-ci": "cross-env TZ=Etc/UTC jest --ci --maxWorkers=100%", "clean": "rm -rf dist node_modules .eslintcache .stylelintcache tsconfig.tsbuildinfo", "stats": "cross-env NODE_ENV=production webpack --profile --json > webpack_stats.json", "mmjstool": "mmjstool", "i18n-extract": "npm run mmjstool -- i18n extract-webapp --webapp-dir ./src", "i18n-clean-empty": "npm run mmjstool -- i18n clean-empty --webapp-dir ./src", "i18n-check-empty-src": "npm run mmjstool -- i18n check-empty-src --webapp-dir ./src", "check-types": "tsc -b", "make-emojis": "node --experimental-json-modules build/emoji/make_emojis.mjs"}}